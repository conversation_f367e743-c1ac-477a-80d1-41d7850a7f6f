package com.lg.produce.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 库存预警VO
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@ApiModel(description = "库存预警信息")
public class StockWarningVO {

    @ApiModelProperty("工单编号")
    private String workOrderCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("计划数量")
    private BigDecimal planQty;

    @ApiModelProperty("完成数量")
    private BigDecimal completedQty;

    @ApiModelProperty("剩余需求")
    private BigDecimal remainingQty;

    @ApiModelProperty("当前库存")
    private BigDecimal totalCurrentStock;

    @ApiModelProperty("可用库存")
    private BigDecimal totalAvailableStock;

    @ApiModelProperty("安全库存")
    private BigDecimal totalSafetyStock;

    @ApiModelProperty("库存缺口")
    private BigDecimal stockGap;

    @ApiModelProperty("预警级别")
    private String stockWarningLevel;

    @ApiModelProperty("库存状态")
    private String stockStatus;

    @ApiModelProperty("计划结束时间")
    private LocalDateTime planEndTime;

    @ApiModelProperty("紧急程度")
    private String urgencyLevel;

    @ApiModelProperty("建议措施")
    private String suggestedAction;
}
