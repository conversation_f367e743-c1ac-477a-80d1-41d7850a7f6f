/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.cache;

import cn.hutool.extra.spring.SpringUtil;
import com.bstek.ureport.definition.ReportDefinition;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.lg.financecloud.common.redis.utils.RedisUtils;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 🔑 混合缓存实现：本地协议使用咖啡缓存，其他协议使用Redis缓存
 * 支持开发模式和生产模式的缓存策略
 * 
 * <AUTHOR> Team
 * @since 2025-01-27
 */
public class HybridReportDefinitionCache implements ReportDefinitionCache {
    
    // 🔑 咖啡缓存 - 10分钟过期，用于生产模式
    private final Cache<String, ReportDefinition> caffeineCache = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(Duration.ofMinutes(10))
            .recordStats()
            .build();
    
    // 🔑 Redis缓存前缀
    public static final String UREPORT_KEY_PREFIX = "gl:ureport_def:";
    
    @Override
    public ReportDefinition getReportDefinition(String file) {
        if (file == null) return null;
        
        // 判断是否为开发模式
        boolean isDevMode = isDevelopmentMode();
        
        // 🔑 本地协议：根据环境选择缓存策略
        if (file.startsWith("local:")) {
            if (isDevMode) {
                // 开发模式：不使用缓存，直接返回null让系统重新解析
                System.out.println("🔧 开发模式：跳过本地模板缓存，强制重新解析: " + file);
                return null;
            } else {
                // 生产模式：使用咖啡缓存
                ReportDefinition cached = caffeineCache.getIfPresent(file);
                if (cached != null) {
                    System.out.println("🔑 从咖啡缓存获取本地模板: " + file);
                    return cached;
                }
            }
        } else {
            // 🔑 其他协议：从Redis获取
            ReportDefinition cached = RedisUtils.getCacheObject(UREPORT_KEY_PREFIX + file);
            if (cached != null) {
                System.out.println("🔑 从Redis缓存获取模板: " + file);
            }
            return cached;
        }
        
        return null;
    }
    
    @Override
    public void cacheReportDefinition(String file, ReportDefinition reportDefinition) {
        if (file == null || reportDefinition == null) return;
        
        // 判断是否为开发模式
        boolean isDevMode = isDevelopmentMode();
        
        // 🔑 本地协议：根据环境选择缓存策略
        if (file.startsWith("local:")) {
            if (isDevMode) {
                // 开发模式：不缓存，直接跳过
                System.out.println("🔧 开发模式：跳过本地模板缓存: " + file);
            } else {
                // 生产模式：存入咖啡缓存
                caffeineCache.put(file, reportDefinition);
                System.out.println("🔑 本地模板已缓存到咖啡缓存: " + file);
            }
        } else {
            // 🔑 其他协议：存入Redis
            RedisUtils.setCacheObject(UREPORT_KEY_PREFIX + file, reportDefinition);
            System.out.println("🔑 模板已缓存到Redis: " + file);
        }
    }
    
    /**
     * 判断是否为开发模式
     */
    private boolean isDevelopmentMode() {
        try {
            String env = SpringUtil.getProperty("ENV");
            return "local".equals(env);
        } catch (Exception e) {
            System.out.println("无法获取环境配置，默认使用生产模式: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 🔑 清除咖啡缓存
     */
    public void clearLocalCache() {
        caffeineCache.invalidateAll();
        System.out.println("🔑 咖啡缓存已清空");
    }
    
    /**
     * 🔑 清除指定本地模板的缓存
     */
    public void clearLocalTemplate(String file) {
        if (file != null && file.startsWith("local:")) {
            caffeineCache.invalidate(file);
            System.out.println("🔑 本地模板缓存已清除: " + file);
        }
    }
    
    /**
     * 🔑 获取缓存统计信息
     */
    public String getLocalCacheStats() {
        boolean isDevMode = isDevelopmentMode();
        if (isDevMode) {
            return "开发模式：不使用缓存";
        } else {
            return String.format("咖啡缓存统计: %s", caffeineCache.stats());
        }
    }
}
