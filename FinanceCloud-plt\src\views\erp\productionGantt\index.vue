<template>
  <div class="production-gantt-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>生产计划执行甘特图</h2>
      <p class="page-description">
        实时监控生产计划执行进度，包含完成数量和库存状态信息
      </p>
    </div>

    <!-- 增强版甘特图组件 -->
    <enhanced-gantt ref="ganttChart" />

    <!-- 帮助说明 -->
    <div class="help-panel" v-if="showHelp">
      <el-card>
        <div slot="header">
          <span>使用说明</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="showHelp = false">
            关闭
          </el-button>
        </div>
        <div class="help-content">
          <h4>甘特图说明：</h4>
          <ul>
            <li><span class="color-box completed"></span> 绿色：已完成工单</li>
            <li><span class="color-box in-progress"></span> 蓝色：进行中工单</li>
            <li><span class="color-box paused"></span> 橙色：已暂停工单</li>
            <li><span class="color-box not-started"></span> 灰色：未开始工单</li>
            <li><span class="color-box cancelled"></span> 深灰色：已取消工单</li>
          </ul>
          
          <h4>库存状态：</h4>
          <ul>
            <li><span class="border-box critical"></span> 红色边框：低于安全库存</li>
            <li><span class="border-box warning"></span> 橙色边框：库存不足</li>
            <li>无边框：库存充足</li>
          </ul>
          
          <h4>操作说明：</h4>
          <ul>
            <li>点击任务条可查看详细信息</li>
            <li>使用筛选条件可快速定位问题工单</li>
            <li>库存预警按钮可查看需要关注的库存问题</li>
            <li>支持导出PDF格式的甘特图</li>
          </ul>
        </div>
      </el-card>
    </div>

    <!-- 浮动帮助按钮 -->
    <el-button 
      class="help-button" 
      type="primary" 
      icon="el-icon-question" 
      circle 
      @click="showHelp = !showHelp">
    </el-button>
  </div>
</template>

<script>
import EnhancedGantt from '@/components/EnhancedGantt'

export default {
  name: 'ProductionGantt',
  components: {
    EnhancedGantt
  },
  data() {
    return {
      showHelp: false
    }
  },
  mounted() {
    // 页面加载完成后的初始化操作
    this.$nextTick(() => {
      // 可以在这里添加一些初始化逻辑
    })
  },
  methods: {
    // 刷新甘特图数据
    refreshGantt() {
      if (this.$refs.ganttChart) {
        this.$refs.ganttChart.loadGanttData()
      }
    },
    
    // 导出甘特图
    exportGantt() {
      if (this.$refs.ganttChart) {
        this.$refs.ganttChart.exportGantt()
      }
    }
  }
}
</script>

<style scoped>
.production-gantt-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.page-header {
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 0;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.help-panel {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  width: 350px;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.help-content h4 {
  margin: 15px 0 10px 0;
  color: #333;
  font-size: 14px;
}

.help-content ul {
  margin: 0 0 15px 0;
  padding-left: 20px;
}

.help-content li {
  margin-bottom: 8px;
  font-size: 13px;
  color: #666;
  display: flex;
  align-items: center;
}

.color-box {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  border-radius: 2px;
}

.color-box.completed { background-color: #4caf50; }
.color-box.in-progress { background-color: #2196f3; }
.color-box.paused { background-color: #ff9800; }
.color-box.not-started { background-color: #e0e0e0; }
.color-box.cancelled { background-color: #9e9e9e; }

.border-box {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  background-color: #f0f0f0;
  border-radius: 2px;
}

.border-box.critical { border: 2px solid #f44336; }
.border-box.warning { border: 2px solid #ff9800; }

.help-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 999;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .help-panel {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .help-panel {
    position: static;
    transform: none;
    width: 100%;
    margin: 20px;
  }
  
  .help-button {
    bottom: 20px;
    right: 20px;
  }
  
  .page-header {
    padding: 15px;
  }
  
  .page-header h2 {
    font-size: 20px;
  }
}
</style>

<style>
/* 全局甘特图样式补充 */
.gantt_task_line.gantt-completed {
  background-color: #4caf50 !important;
  border-color: #388e3c !important;
}

.gantt_task_line.gantt-in-progress {
  background-color: #2196f3 !important;
  border-color: #1976d2 !important;
}

.gantt_task_line.gantt-paused {
  background-color: #ff9800 !important;
  border-color: #f57c00 !important;
}

.gantt_task_line.gantt-not-started {
  background-color: #e0e0e0 !important;
  border-color: #bdbdbd !important;
}

.gantt_task_line.gantt-cancelled {
  background-color: #9e9e9e !important;
  border-color: #757575 !important;
}

.gantt_task_line.stock-critical {
  border: 3px solid #f44336 !important;
  border-radius: 3px !important;
}

.gantt_task_line.stock-warning {
  border: 2px solid #ff9800 !important;
  border-radius: 3px !important;
}

/* 甘特图表格样式 */
.gantt_grid_scale .gantt_grid_head_cell {
  background-color: #f5f5f5 !important;
  border-color: #e0e0e0 !important;
  font-weight: 500 !important;
}

.gantt_row.gantt_selected {
  background-color: #e3f2fd !important;
}

.gantt_task_cell {
  border-color: #e0e0e0 !important;
}

/* 库存状态样式 */
.stock-status.sufficient {
  color: #4caf50;
  font-weight: 500;
}

.stock-status.insufficient {
  color: #ff9800;
  font-weight: 500;
}

.stock-status.none {
  color: #f44336;
  font-weight: 500;
}
</style>
