<template>
    <div class="conten_body_main margintop10">
        <top-search @query="fun_getTableListData" @reset="fun_searchReset" ref="topSearch">
            <template #otherContion>
                <vxe-form-item :title="$t('报表日期')" :item-render="{}" span="24">
                    <template #default>
                        <el-date-picker
                            size="mini"
                            v-model="searchFormData.reportDateRange"
                            type="daterange"
                            unlink-panels
                            :range-separator="$t('至')"
                            :start-placeholder="$t('开始日期')"
                            :end-placeholder="$t('结束日期')"
                            value-format="yyyy-MM-dd">
                        </el-date-picker>
                    </template>
                </vxe-form-item>
            </template>
            <template #toolBar>
                <vxe-button :content="$t('刷新')" @click="fun_getTableListData"></vxe-button>
                <vxe-button :content="$t('导出')" icon="el-icon-download" @click="fun_exportExcel"></vxe-button>
                <vxe-button :content="$t('打印')" icon="el-icon-printer" @click="fun_printReport"></vxe-button>
                <vxe-toolbar ref="xToolbar" :custom="{icon:'vxe-button--icon vxe-icon-custom-column'}"></vxe-toolbar>
            </template>
        </top-search>
        
        <el-container>
            <el-main>


                <!-- 详细数据表格 -->
                <vxe-table 
                    :loading="loading" 
                    class="mytable-scrollbar" 
                    show-overflow="tooltip" 
                    size="mini" 
                    ref="ListTableRef"  
                    border 
                    stripe 
                    header-align="left"
                    :height="contentStyleObj.height" 
                    :data="tableList" 
                    row-id="id"
                    id="productionDailyReportTableList" 
                    resizable 
                    :custom-config="{storage: true}"
                    :filter-config="{remote:true}" 
                    @filter-change="handleFilterChange"
                    :sort-config="{remote:true}" 
                    @sort-change="handeleSortChange">
                    
                    <vxe-column field="reportDate" :title="$t('报表日期')" width="120" sortable>
                        <template #default="{row}">{{ formatDate(row.reportDate) }}</template>
                    </vxe-column>
                    
                    <vxe-column field="workCenterName" :title="$t('工作中心')" min-width="150" :params="{searchType:'text', fuzzySearch: true}" sortable>
                         <template #default="{row}">
                             <span v-if="row.workCenterName && row.workCenterCode">
                                 {{ row.workCenterName }}（{{ row.workCenterCode }}）
                             </span>
                             <span v-else-if="row.workCenterName">
                                 {{ row.workCenterName }}
                             </span>
                             <span v-else class="text-muted">-</span>
                         </template>
                     </vxe-column>

                     <vxe-column field="materialName" :title="$t('物料信息')" min-width="200" :params="{searchType:'text', fuzzySearch: true}">
                         <template #default="{row}">
                             <span v-if="row.materialName && row.materialCode">
                                 {{ row.materialName }}（{{ row.materialCode }}）
                             </span>
                             <span v-else-if="row.materialName">
                                 {{ row.materialName }}
                             </span>
                             <span v-else class="text-muted">-</span>
                         </template>
                     </vxe-column>

                    <vxe-column field="operatorName" :title="$t('操作员')" width="120" :params="{searchType:'text', fuzzySearch: true}">
                        <template #default="{row}">
                            {{ row.operatorName || '-' }}
                        </template>
                    </vxe-column>
                    
                                         <vxe-column field="equipmentInfo" :title="$t('设备信息')" min-width="150" show-overflow="tooltip">
                         <template #default="{row}">
                             <span v-if="row.equipmentInfo" class="text-info">
                                 {{ row.equipmentInfo.split('-')[1] }}（{{ row.equipmentInfo.split('-')[0] }}）
                             </span>
                             <span v-else class="text-muted">-</span>
                         </template>
                     </vxe-column>
                    
                    <vxe-column field="taskCount" :title="$t('任务数')" width="80" sortable></vxe-column>
                    
                    <vxe-column field="totalCompletedQty" :title="$t('完成数量')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-primary">{{ row.totalCompletedQty }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalQualifiedQty" :title="$t('合格数量')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-success">{{ row.totalQualifiedQty }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalDefectiveQty" :title="$t('不良数量')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-danger">{{ row.totalDefectiveQty }}</span>
                        </template>
                    </vxe-column>
                    
                                         <vxe-column field="qualifiedRate" :title="$t('合格率')" width="100" sortable visible="false">
                         <template #default="{row}">
                             <span :class="getQualifiedRateClass(row.qualifiedRate)">
                                 {{ row.qualifiedRate }}%
                             </span>
                         </template>
                     </vxe-column>
                     
                     <vxe-column field="defectiveRate" :title="$t('不良率')" width="100" sortable visible="false">
                         <template #default="{row}">
                             <span :class="getDefectiveRateClass(row.defectiveRate)">
                                 {{ row.defectiveRate }}%
                             </span>
                         </template>
                     </vxe-column>
                    
                    <vxe-column field="exceptionCount" :title="$t('异常次数')" width="100" sortable>
                        <template #default="{row}">
                            <span v-if="row.exceptionCount > 0" class="text-warning">{{ row.exceptionCount }}</span>
                            <span v-else class="text-muted">0</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="downtimeCount" :title="$t('停机次数')" width="100" sortable>
                        <template #default="{row}">
                            <span v-if="row.downtimeCount > 0" class="text-danger">{{ row.downtimeCount }}</span>
                            <span v-else class="text-muted">0</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalDowntimeDuration" :title="$t('停机时长(小时)')" width="120" sortable>
                        <template #default="{row}">
                            <span v-if="row.totalDowntimeDuration > 0" class="text-danger">
                                {{ row.totalDowntimeDuration }}
                            </span>
                            <span v-else class="text-muted">0</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="avgTaskDurationMinutes" :title="$t('平均时长(分钟)')" width="120" sortable>
                        <template #default="{row}">
                            {{ Math.round(row.avgTaskDurationMinutes) }}
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalEquipmentHours" :title="$t('设备工时')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-info">{{ row.totalEquipmentHours || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="totalStaffHours" :title="$t('人员工时')" width="100" sortable>
                        <template #default="{row}">
                            <span class="text-success">{{ row.totalStaffHours || 0 }}</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="exceptionTypes" :title="$t('异常类型')" min-width="150" show-overflow="tooltip">
                        <template #default="{row}">
                            <span v-if="row.exceptionTypes" class="text-warning">{{ row.exceptionTypes }}</span>
                            <span v-else class="text-muted">-</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="downtimeReasons" :title="$t('停机原因')" min-width="150" show-overflow="tooltip">
                        <template #default="{row}">
                            <span v-if="row.downtimeReasons" class="text-danger">{{ row.downtimeReasons }}</span>
                            <span v-else class="text-muted">-</span>
                        </template>
                    </vxe-column>
                    
                    <vxe-column field="firstTaskStart" :title="$t('首次开始')" width="120">
                        <template #default="{row}">{{ formatDateTime(row.firstTaskStart) }}</template>
                    </vxe-column>
                    
                    <vxe-column field="lastTaskEnd" :title="$t('最后结束')" width="120">
                        <template #default="{row}">{{ formatDateTime(row.lastTaskEnd) }}</template>
                    </vxe-column>
                </vxe-table>
                
                <vxe-pager
                    align="center"
                    size="mini"
                    :current-page.sync="page.current"
                    :page-size.sync="page.size"
                    :total="page.total"
                    perfect
                    background
                    :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
                    @page-change="handlePageChange">
                </vxe-pager>
            </el-main>
        </el-container>
    </div>
</template>

<script>
import TopSearch from "@/components/filterate-search/index";
import { getTableListData, exportProductionDailyDetail } from "@/api/erp/productionDailyReport";

export default {
    name: "ProductionDailyReportList",
    components: {
        TopSearch
    },
    data() {
        return {
            loading: false,
            tableList: [],
            searchFormData: {
                reportDateRange: null
            },
            page: {
                current: 1,
                size: 20,
                total: 0
            },
            contentStyleObj: {
                height: '600px'
            },
            checkList: []
        };
    },
    mounted() {
        this.fun_getTableListData();
    },
    methods: {
        // 获取表格数据
        fun_getTableListData(filterParam) {
            this.loading = true;

            let pages = {
                page: this.page.current,
                limit: this.page.size
            };

            // 处理自定义日期范围查询
            let searchParams = { ...this.searchFormData };
            if (searchParams.reportDateRange && searchParams.reportDateRange.length === 2) {
                // 后台现在接受字符串格式的日期参数
                searchParams.startDate = searchParams.reportDateRange[0];
                searchParams.endDate = searchParams.reportDateRange[1];
                delete searchParams.reportDateRange;
            }

            let params = Object.assign(pages, searchParams, filterParam, { toUnderLineCase: false });
            Object.assign(this.searchFormData, {}, filterParam);
            
            getTableListData(params).then(res => {
                const payload = res && res.data ? res.data : {};
                if (payload && (payload.code === 0 || payload.returnCode === '0')) {
                    // 兼容后端返回 {code:0, data:[...], total:?}
                    const rows = Array.isArray(payload.data)
                        ? payload.data
                        : (payload.records || payload.rows || []);
                    this.tableList = rows;
                    this.page.total = payload.total || rows.length || 0;
                } else if (res.status === 200) {
                    // 兜底：常见的分页结构 {records,total} 或直接数组
                    this.tableList = payload.records || payload.rows || payload.data || [];
                    this.page.total = payload.total || this.tableList.length || 0;
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            });
        },
        

        
        // 搜索重置
        fun_searchReset() {
            this.page.current = 1;
            this.fun_getTableListData();
        },
        
        // 分页变化
        handlePageChange({ currentPage, pageSize }) {
            this.page.current = currentPage;
            this.page.size = pageSize;
            this.fun_getTableListData();
        },
        
        // 排序变化
        handeleSortChange({ property, order }) {
            let sort = { field: property, order: order };
            this.fun_getTableListData(sort);
        },
        
        // 筛选变化
        handleFilterChange({ filters }) {
            let filterParamArray = [];
            filters.forEach(item => {
                filterParamArray.push(item.datas[0].filterSos);
            });
            if (filterParamArray.length > 0) {
                this.fun_getTableListData({ filterSos: JSON.stringify(filterParamArray) });
            } else {
                this.fun_getTableListData({ filterSos: [] });
            }
        },
        
        // 导出Excel
        fun_exportExcel() {
            // 处理自定义日期范围查询参数
            let exportParams = { ...this.searchFormData };
            if (exportParams.reportDateRange && exportParams.reportDateRange.length === 2) {
                // 后台现在接受字符串格式的日期参数
                exportParams.startDate = exportParams.reportDateRange[0];
                exportParams.endDate = exportParams.reportDateRange[1];
                delete exportParams.reportDateRange;
            }

            const params = Object.assign({}, exportParams, { toUnderLineCase: false });

            // 使用 commonExportFile 方法
            this.commonExportFile({
                api: (params) => exportProductionDailyDetail(params, 'excel'),
                params: params,
                fileName: '生产日报',
                fileExt: 'xlsx',
                fileNameNeedTimeSpan: true,
                fileType: 'excel',
                successMsg: '生产日报导出成功',
                loadingText: '正在导出生产日报，请稍候...'
            });
        },
        
        // 打印报表
        fun_printReport() {
            // 处理自定义日期范围查询参数
            let printParams = { ...this.searchFormData };
            if (printParams.reportDateRange && printParams.reportDateRange.length === 2) {
                // 后台现在接受字符串格式的日期参数
                printParams.startDate = printParams.reportDateRange[0];
                printParams.endDate = printParams.reportDateRange[1];
                delete printParams.reportDateRange;
            }

            const params = Object.assign({}, printParams, { toUnderLineCase: false });

            // 使用 commonExportFile 方法处理 PDF 打印
            this.commonExportFile({
                api: (params) => exportProductionDailyDetail(params, 'pdf'),
                params: params,
                fileName: '生产日报打印版',
                fileExt: 'pdf',
                fileNameNeedTimeSpan: true,
                fileType: 'pdf',
                successMsg: '生产日报打印版生成成功',
                loadingText: '正在生成打印版本，请稍候...'
            });
        },

        // 搜索重置
        fun_searchReset() {
            this.searchFormData = {
                reportDateRange: null
            };
            this.page.current = 1;
            this.fun_getTableListData();
        },

        // 格式化时间
        formatTime(timestr) {
            try {
                if (timestr) {
                    return timestr.substr(0, 10);
                } else {
                    return '';
                }
            } catch (e) {
                return '';
            }
        },
        
        // 新增：格式化日期（复用 formatTime）
        formatDate(dateStr) {
            return this.formatTime(dateStr);
        },
        
        // 格式化日期时间
        formatDateTime(dateTime) {
            return this.formatTime(dateTime);
        },
        
        // 获取合格率样式类
        getQualifiedRateClass(rate) {
            if (rate >= 95) return 'text-success';
            if (rate >= 85) return 'text-warning';
            return 'text-danger';
        },
        
        // 获取不良率样式类
        getDefectiveRateClass(rate) {
            if (rate <= 5) return 'text-success';
            if (rate <= 15) return 'text-warning';
            return 'text-danger';
        },
        

    }
};
</script>

<style scoped>


.text-primary {
    color: #409EFF;
}

.text-success {
    color: #67C23A;
}

.text-warning {
    color: #E6A23C;
}

.text-danger {
    color: #F56C6C;
}

.text-muted {
    color: #C0C4CC;
}

.mytable-scrollbar {
    margin-top: 20px;
}



/* 表格样式调整 - 保持与现有功能一致 */
::v-deep .vxe-table .vxe-body--column {
    padding: 12px 8px;
    line-height: 1.4;
}

/* 汇总看板底部边距 */
.summary-cards {
    margin-bottom: 30px;
}
</style>
