package com.lg.produce.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lg.financecloud.common.core.util.R;
import com.lg.produce.entity.ErpTaskReport;
import com.lg.produce.vo.ErpTaskReportVO;
import com.lg.produce.vo.ErpTaskReportEquipmentVO;
import com.lg.produce.vo.ErpTaskReportStaffVO;
import com.lg.produce.vo.ProductionDailyReportVO;
import com.lg.produce.vo.ProductionDailySummaryVO;
import com.lg.produce.vo.ProductionPlanExecutionVO;
import com.lg.produce.vo.ProductionPlanExecutionSummaryVO;
import com.lg.produce.vo.EnhancedGanttVO;
import com.lg.produce.vo.StockWarningVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 工序任务报工 服务接口
 */
public interface ErpTaskReportService {

    /**
     * 分页查询报工记录
     *
     * @param page 分页参数
     * @param queryVO 查询条件
     * @return 分页结果
     */
    IPage<ErpTaskReportVO> pageList(Page<ErpTaskReport> page, ErpTaskReportVO queryVO);

    /**
     * 根据ID查询报工详情
     *
     * @param id 报工ID
     * @return 报工详情
     */
    ErpTaskReportVO getDetailById(String id);

    /**
     * 创建工序任务报工
     *
     * @param reportVO 报工信息
     * @return 创建结果
     */
    R<ErpTaskReportVO> createTaskReport(ErpTaskReportVO reportVO);

    /**
     * 更新工序任务报工
     *
     * @param reportVO 报工信息
     * @return 更新结果
     */
    R<ErpTaskReportVO> updateTaskReport(ErpTaskReportVO reportVO);

    /**
     * 删除工序任务报工
     *
     * @param id 报工ID
     * @return 删除结果
     */
    R<Boolean> deleteTaskReport(String id);

    /**
     * 根据工单ID查询报工记录列表
     *
     * @param workOrderId 工单ID
     * @return 报工记录列表
     */
    List<ErpTaskReportVO> getReportsByWorkOrderId(String workOrderId);

    /**
     * 根据任务ID查询报工记录列表
     *
     * @param taskId 任务ID
     * @return 报工记录列表
     */
    List<ErpTaskReportVO> getReportsByTaskId(String taskId);

    /**
     * 统计任务已报工数量
     *
     * @param taskId 任务ID
     * @return 已报工数量
     */
    BigDecimal sumReportedQuantity(String taskId);
    
    /**
     * 获取工作中心的设备列表
     *
     * @param workCenterId 工作中心ID
     * @return 设备列表
     */
    R<List<ErpTaskReportEquipmentVO>> getWorkCenterEquipments(String workCenterId);
    
    /**
     * 获取工作中心的人员列表
     *
     * @param workCenterId 工作中心ID
     * @return 人员列表
     */
    R<List<ErpTaskReportStaffVO>> getWorkCenterStaff(String workCenterId);
    
    /**
     * 查询详细生产日报
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param workCenterId 工作中心ID（可选）
     * @param operatorName 操作员姓名（可选）
     * @return 生产日报列表
     */
    R<List<ProductionDailyReportVO>> queryProductionDailyReport(
            String startDate,
            String endDate,
            String workCenterId,
            String operatorName
    );
    
    /**
     * 查询生产日报汇总
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @return 生产日报汇总列表
     */
    R<List<ProductionDailySummaryVO>> queryProductionDailySummary(
            String startDate,
            String endDate
    );
    
    /**
     * 查询生产计划执行情况明细
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param workCenterId 工作中心ID（可选）
     * @param planStatus 计划状态（可选）
     * @return 生产计划执行情况列表
     */
    R<List<ProductionPlanExecutionVO>> queryProductionPlanExecution(
            String startDate,
            String endDate,
            String workCenterId,
            String planStatus
    );
    
    /**
     * 查询生产计划执行情况汇总
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @return 生产计划执行情况汇总列表
     */
    R<List<ProductionPlanExecutionSummaryVO>> queryProductionPlanExecutionSummary(
            String startDate,
            String endDate
    );

    /**
     * 导出/打印生产日报
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param workCenterId 工作中心ID（可选）
     * @param operatorName 操作员姓名（可选）
     * @param format 导出格式：excel/pdf
     * @param response HTTP响应对象
     */
    void exportProductionDailyReport(
            String startDate,
            String endDate,
            String workCenterId,
            String operatorName,
            String format,
            javax.servlet.http.HttpServletResponse response
    );

    /**
     * 导出/打印生产日报汇总
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param format 导出格式：excel/pdf
     * @param response HTTP响应对象
     */
    void exportProductionDailySummary(
            String startDate,
            String endDate,
            String format,
            javax.servlet.http.HttpServletResponse response
    );

    /**
     * 查询增强版甘特图数据（包含库存信息）
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param workCenterId 工作中心ID（可选）
     * @param stockFilter 库存筛选条件（可选）
     * @return 增强版甘特图数据列表
     */
    R<List<EnhancedGanttVO>> queryEnhancedGanttData(
            String startDate,
            String endDate,
            String workCenterId,
            String stockFilter
    );

    /**
     * 查询库存预警信息
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @return 库存预警列表
     */
    R<List<StockWarningVO>> queryStockWarnings(
            String startDate,
            String endDate
    );
}