package com.lg.produce.controller;

import com.lg.financecloud.common.core.util.R;
import com.lg.produce.service.ErpTaskReportService;
import com.lg.produce.vo.EnhancedGanttVO;
import com.lg.produce.vo.StockWarningVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 增强版甘特图控制器
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/api/production/gantt")
@RequiredArgsConstructor
@Api(tags = "增强版生产甘特图接口")
public class EnhancedGanttController {

    private final ErpTaskReportService taskReportService;

    /**
     * 获取增强版甘特图数据（包含库存信息）
     */
    @GetMapping("/enhanced-data")
    @ApiOperation(value = "获取增强版甘特图数据", notes = "获取包含库存信息的生产计划执行甘特图数据")
    public R<List<EnhancedGanttVO>> getEnhancedGanttData(
            @ApiParam(value = "开始日期，格式：yyyy-MM-dd") @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期，格式：yyyy-MM-dd") @RequestParam(required = false) String endDate,
            @ApiParam(value = "工作中心ID") @RequestParam(required = false) String workCenterId,
            @ApiParam(value = "库存筛选条件：sufficient-库存充足，insufficient-库存不足，none-无库存，below_safety-低于安全库存") 
            @RequestParam(required = false) String stockFilter) {
        
        log.info("获取增强版甘特图数据，参数：startDate={}, endDate={}, workCenterId={}, stockFilter={}", 
                startDate, endDate, workCenterId, stockFilter);
        
        return taskReportService.queryEnhancedGanttData(startDate, endDate, workCenterId, stockFilter);
    }

    /**
     * 获取库存预警信息
     */
    @GetMapping("/stock-warnings")
    @ApiOperation(value = "获取库存预警信息", notes = "获取生产计划相关的库存预警信息")
    public R<List<StockWarningVO>> getStockWarnings(
            @ApiParam(value = "开始日期，格式：yyyy-MM-dd") @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期，格式：yyyy-MM-dd") @RequestParam(required = false) String endDate) {
        
        log.info("获取库存预警信息，参数：startDate={}, endDate={}", startDate, endDate);
        
        return taskReportService.queryStockWarnings(startDate, endDate);
    }

    /**
     * 获取甘特图统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation(value = "获取甘特图统计信息", notes = "获取生产计划执行的统计数据")
    public R<Object> getGanttStatistics(
            @ApiParam(value = "开始日期，格式：yyyy-MM-dd") @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期，格式：yyyy-MM-dd") @RequestParam(required = false) String endDate,
            @ApiParam(value = "工作中心ID") @RequestParam(required = false) String workCenterId) {
        
        try {
            // 获取甘特图数据
            R<List<EnhancedGanttVO>> ganttDataResult = taskReportService.queryEnhancedGanttData(
                    startDate, endDate, workCenterId, null);
            
            if (!ganttDataResult.isSuccess()) {
                return R.failed("获取统计数据失败");
            }
            
            List<EnhancedGanttVO> ganttData = ganttDataResult.getData();
            
            // 计算统计信息
            int totalWorkOrders = ganttData.size();
            long completedCount = ganttData.stream()
                    .filter(item -> item.getStatus() == 4)
                    .count();
            long inProgressCount = ganttData.stream()
                    .filter(item -> item.getStatus() == 2)
                    .count();
            long delayedCount = ganttData.stream()
                    .filter(item -> item.getDelayDays() != null && item.getDelayDays() > 0)
                    .count();
            long stockWarningCount = ganttData.stream()
                    .filter(item -> "低于安全库存".equals(item.getStockWarningLevel()) || 
                                   "库存不足".equals(item.getStockWarningLevel()))
                    .count();
            
            double avgCompletionRate = ganttData.stream()
                    .filter(item -> item.getCompletionRate() != null)
                    .mapToDouble(item -> item.getCompletionRate().doubleValue())
                    .average()
                    .orElse(0.0);
            
            // 构建统计结果
            java.util.Map<String, Object> statistics = new java.util.HashMap<>();
            statistics.put("totalWorkOrders", totalWorkOrders);
            statistics.put("completedCount", completedCount);
            statistics.put("inProgressCount", inProgressCount);
            statistics.put("delayedCount", delayedCount);
            statistics.put("stockWarningCount", stockWarningCount);
            statistics.put("avgCompletionRate", Math.round(avgCompletionRate * 100.0) / 100.0);
            statistics.put("completionRate", totalWorkOrders > 0 ? 
                    Math.round((double) completedCount / totalWorkOrders * 100.0) / 100.0 : 0.0);
            
            return R.ok(statistics);
            
        } catch (Exception e) {
            log.error("获取甘特图统计信息失败", e);
            return R.failed("获取统计信息失败: " + e.getMessage());
        }
    }
}
