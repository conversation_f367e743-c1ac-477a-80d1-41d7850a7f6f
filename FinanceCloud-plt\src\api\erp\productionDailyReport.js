import request from '@/router/axios'

/**
 * 获取生产日报数据
 * @param {Object} params 查询参数
 * @param {String} type 类型：'detail' 或 'summary'
 * @returns {Promise}
 */
export function getTableListData(params, type = 'detail') {
  const url = type === 'summary'
    ? '/flowable/api/production/work-reports/daily-summary'
    : '/flowable/api/production/work-reports/daily-report';

  return request({
    url: url,
    method: 'get',
    params: params
  })
}

/**
 * 导出/打印生产日报详细数据
 * @param {Object} params 查询参数
 * @param {String} format 导出格式：excel/pdf
 * @returns {Promise}
 */
export function exportProductionDailyDetail(params, format = 'excel') {
  return request({
    url: '/flowable/api/production/work-reports/export',
    method: 'get',
    params: { ...params, format },
    responseType: 'blob'
  })
}

/**
 * 导出/打印生产日报汇总数据
 * @param {Object} params 查询参数
 * @param {String} format 导出格式：excel/pdf
 * @returns {Promise}
 */
export function exportProductionDailySummary(params, format = 'excel') {
  return request({
    url: '/flowable/api/production/work-reports/daily-summary/export',
    method: 'get',
    params: { ...params, format },
    responseType: 'blob'
  })
}
