package com.lg.produce.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lg.produce.entity.ErpTaskReport;
import com.lg.produce.vo.ErpTaskReportVO;
import com.lg.produce.vo.ProductionDailyReportVO;
import com.lg.produce.vo.ProductionDailySummaryVO;
import com.lg.produce.vo.ProductionPlanExecutionVO;
import com.lg.produce.vo.ProductionPlanExecutionSummaryVO;
import com.lg.produce.vo.EnhancedGanttVO;
import com.lg.produce.vo.StockWarningVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 工序任务报工 Mapper 接口
 */
@Mapper
public interface ErpTaskReportMapper extends BaseMapper<ErpTaskReport> {

    /**
     * 分页查询报工记录
     *
     * @param page 分页参数
     * @param workOrderId 工单ID
     * @param taskId 任务ID
     * @param workCenterId 工作中心ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分页结果
     */
    IPage<ErpTaskReportVO> selectReportPage(Page<ErpTaskReport> page,
                                           @Param("workOrderId") String workOrderId,
                                           @Param("taskId") String taskId,
                                           @Param("workCenterId") String workCenterId,
                                           @Param("startDate") String startDate,
                                           @Param("endDate") String endDate);

    /**
     * 根据ID查询报工详情
     *
     * @param id 报工ID
     * @return 报工详情
     */
    ErpTaskReportVO selectReportDetail(@Param("id") String id);

    /**
     * 统计任务已报工数量
     *
     * @param taskId 任务ID
     * @return 已报工数量
     */
    BigDecimal sumReportedQuantity(@Param("taskId") String taskId);
    
    /**
     * 根据工作中心ID查询工作中心名称
     *
     * @param workCenterId 工作中心ID
     * @return 工作中心名称
     */
    String selectWorkCenterNameById(@Param("workCenterId") String workCenterId);
    
    /**
     * 根据工单ID查询报工记录列表
     *
     * @param workOrderId 工单ID
     * @return 报工记录列表
     */
    List<ErpTaskReportVO> selectReportsByWorkOrderId(@Param("workOrderId") String workOrderId);
    
    /**
     * 根据任务ID查询报工记录列表
     *
     * @param taskId 任务ID
     * @return 报工记录列表
     */
    List<ErpTaskReportVO> selectReportsByTaskId(@Param("taskId") String taskId);
    
    /**
     * 查询工序任务的数量统计（仅包括报工数量）
     *
     * @param workOrderId 工单ID
     * @param operationIds 工序ID列表
     * @return 工序数量统计结果，key为工序ID，value为数量统计信息
     */
    List<Map<String, Object>> selectTaskQuantitiesFromReports(@Param("workOrderId") String workOrderId,
                                                              @Param("operationIds") List<String> operationIds);
    
    /**
     * 查询工序任务的报废数量统计
     *
     * @param workOrderId 工单ID
     * @param operationIds 工序ID列表
     * @return 工序报废数量统计结果，key为工序ID，value为报废数量统计信息
     */
    List<Map<String, Object>> selectScrapQuantitiesFromApplications(@Param("workOrderId") String workOrderId,
                                                                     @Param("operationIds") List<String> operationIds);
    
    /**
     * 查询工序返工单的报工数量统计
     *
     * @param workOrderId 工单ID
     * @param operationIds 工序ID列表
     * @return 工序返工单数量统计结果，key为工序ID，value为返工单数量统计信息
     */
    List<Map<String, Object>> selectReworkTaskQuantitiesFromReports(@Param("workOrderId") String workOrderId,
                                                                     @Param("operationIds") List<String> operationIds);
    
    /**
     * 查询详细生产日报
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param workCenterId 工作中心ID（可选）
     * @param operatorName 操作员姓名（可选）
     * @return 生产日报列表
     */
    List<ProductionDailyReportVO> queryProductionDailyReport(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("workCenterId") String workCenterId,
            @Param("operatorName") String operatorName
    );
    
    /**
     * 查询生产日报汇总
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 生产日报汇总列表
     */
    List<ProductionDailySummaryVO> queryProductionDailySummary(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );
    
    /**
     * 查询生产计划执行情况明细
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param workCenterId 工作中心ID（可选）
     * @param planStatus 计划状态（可选）
     * @return 生产计划执行情况列表
     */
    List<ProductionPlanExecutionVO> queryProductionPlanExecution(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("workCenterId") String workCenterId,
            @Param("planStatus") String planStatus
    );
    
    /**
     * 查询生产计划执行情况汇总
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 生产计划执行情况汇总列表
     */
    List<ProductionPlanExecutionSummaryVO> queryProductionPlanExecutionSummary(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );

    /**
     * 查询增强版甘特图数据（包含库存信息）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param workCenterId 工作中心ID（可选）
     * @param stockFilter 库存筛选条件（可选）
     * @return 增强版甘特图数据列表
     */
    List<EnhancedGanttVO> queryEnhancedGanttData(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("workCenterId") String workCenterId,
            @Param("stockFilter") String stockFilter
    );

    /**
     * 查询库存预警信息
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 库存预警列表
     */
    List<StockWarningVO> queryStockWarnings(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );
}