package com.lg.financecloud.common.report.provider;

import cn.hutool.core.io.IoUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.bstek.ureport.provider.report.ReportProvider;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;

/**
 * 简化的本地报表提供者
 * 支持从resources目录读取报表模板
 *
 * <AUTHOR> UReport Team
 * @since 2025-08-23
 */
@Slf4j
public class EnhancedLocalReportProvider implements ReportProvider {

    private static final String LOCAL_PREFIX = "local:";

    // 🔑 参考SqlTemplateManager的做法：预定义搜索路径
    private static final String[] TEMPLATE_LOCATIONS = {
        "classpath*:reports/**/*.ureport.xml"
//        "classpath*:template/**/*.ureport.xml",
//        "classpath*:report-templates/**/*.ureport.xml"
    };

    private final PathMatchingResourcePatternResolver resourceResolver = new PathMatchingResourcePatternResolver();
    
    // 咖啡缓存 - 10分钟过期
    private final Cache<String, String> caffeineCache = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(Duration.ofMinutes(10))
            .recordStats()
            .build();

    // 🔑 构造函数中初始化加载所有模板
    public EnhancedLocalReportProvider() {
        loadAllTemplates();
    }
    
    @Override
    public InputStream loadReport(String file) {
        // 🔑 只处理带协议前缀的情况：local:templateName
        if (!file.startsWith(LOCAL_PREFIX)) {
            return null;
        }

        String templateCode = file.substring(LOCAL_PREFIX.length());
        log.debug("加载本地报表模板: {}", templateCode);

        try {
            String content = getTemplateContent(templateCode);
            if (content != null) {
                log.info("✅ 成功加载本地报表模板: {}", templateCode);
                return new ByteArrayInputStream(content.getBytes("UTF-8"));
            } else {
                log.warn("❌ 本地报表模板不存在: {}", templateCode);
                return null;
            }
        } catch (Exception e) {
            log.error("❌ 加载本地报表模板失败: {}", templateCode, e);
            return null;
        }
    }

    /**
     * 获取模板内容
     */
    /**
     * 🔑 智能获取模板内容：兼容带后缀和不带后缀的情况
     * 开发模式下不使用缓存，生产模式下使用咖啡缓存
     */
    private String getTemplateContent(String templateCode) {
        log.debug("🔍 获取本地模板: {}", templateCode);
        
        // 判断是否为开发模式
        boolean isDevMode = isDevelopmentMode();
        
        if (isDevMode) {
            log.debug("🔧 开发模式：直接从文件系统加载模板，不使用缓存");
            return loadTemplateFromFileSystem(templateCode);
        } else {
            log.debug("🏭 生产模式：使用咖啡缓存");
            return loadTemplateWithCache(templateCode);
        }
    }
    
    /**
     * 判断是否为开发模式
     */
    private boolean isDevelopmentMode() {
        try {
            String env = SpringUtil.getProperty("ENV");
            return "local".equals(env);
        } catch (Exception e) {
            log.warn("无法获取环境配置，默认使用生产模式: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 开发模式：直接从文件系统加载
     */
    private String loadTemplateFromFileSystem(String templateCode) {
        try {
            for (String location : TEMPLATE_LOCATIONS) {
                Resource[] resources = resourceResolver.getResources(location);
                for (Resource resource : resources) {
                    String filename = resource.getFilename();
                    if (filename != null && isTemplateMatch(templateCode, filename)) {
                        try (InputStream inputStream = resource.getInputStream()) {
                            String content = IoUtil.read(inputStream, StandardCharsets.UTF_8);
                            log.debug("✅ 开发模式直接加载模板: {} -> {}", templateCode, filename);
                            return content;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("开发模式加载模板失败: {}", templateCode, e);
        }
        return null;
    }
    
    /**
     * 生产模式：使用咖啡缓存
     */
    private String loadTemplateWithCache(String templateCode) {
        // 先从咖啡缓存获取
        String content = caffeineCache.getIfPresent(templateCode);
        if (content != null) {
            log.debug("✅ 咖啡缓存命中: {}", templateCode);
            return content;
        }
        
        // 缓存未命中，从文件系统加载
        content = loadTemplateFromFileSystem(templateCode);
        if (content != null) {
            // 存入咖啡缓存
            caffeineCache.put(templateCode, content);
            log.debug("✅ 从文件系统加载并存入咖啡缓存: {}", templateCode);
            return content;
        }
        
        log.debug("❌ 模板不存在: {} (咖啡缓存统计: {})", templateCode, caffeineCache.stats());
        return null;
    }
    

    
    /**
     * 判断模板是否匹配
     */
    private boolean isTemplateMatch(String templateCode, String filename) {
        if (filename == null) return false;
        
        // 直接匹配
        if (filename.equals(templateCode)) return true;
        
        // 去掉后缀匹配
        String baseName = filename.replaceAll("\\.(ureport\\.xml|ureport)$", "");
        String inputBaseName = templateCode.replaceAll("\\.(ureport\\.xml|ureport)$", "");
        if (baseName.equals(inputBaseName)) return true;
        
        // 添加后缀匹配
        if (!templateCode.contains(".")) {
            String[] suffixes = {".ureport.xml", ".ureport"};
            for (String suffix : suffixes) {
                if (filename.equals(templateCode + suffix)) return true;
            }
        }
        
        return false;
    }
    
    @Override
    public void deleteReport(String file) {
        log.warn("本地报表模板不支持删除操作: {}", file);
        throw new UnsupportedOperationException("本地报表模板不支持删除操作");
    }
    
    @Override
    public List<com.bstek.ureport.provider.report.ReportFile> getReportFiles() {
        List<com.bstek.ureport.provider.report.ReportFile> files = new ArrayList<>();

        try {
            // 🔑 使用新的搜索路径数组
            for (String location : TEMPLATE_LOCATIONS) {
                Resource[] resources = resourceResolver.getResources(location);

                for (Resource resource : resources) {
                    String filename = resource.getFilename();
                    if (filename != null && filename.endsWith(".ureport.xml")) {
                        String templateCode = filename.substring(0, filename.length() - 12);
                        files.add(new com.bstek.ureport.provider.report.ReportFile(
                                templateCode,
                                new Date()
                        ));
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取本地报表文件列表失败", e);
        }

        return files;
    }
    
    @Override
    public void saveReport(String file, String content) {
        log.warn("本地报表模板不支持保存操作: {}", file);
        throw new UnsupportedOperationException("本地报表模板不支持保存操作，请直接修改resources目录下的文件");
    }
    
    @Override
    public String getName() {
        return "本地文件存储";
    }
    
    @Override
    public boolean disabled() {
        return false;
    }
    
    @Override
    public String getPrefix() {
        return LOCAL_PREFIX;
    }
    
    /**
     * 检查模板是否存在
     * @param templateCode 模板代码
     * @return 是否存在
     */
    public boolean hasTemplate(String templateCode) {
        return getTemplateContent(templateCode) != null;
    }

    /**
     * 🔑 初始化时预加载模板到咖啡缓存（仅生产模式）
     */
    private void loadAllTemplates() {
        boolean isDevMode = isDevelopmentMode();
        if (isDevMode) {
            log.info("🔧 开发模式：跳过模板预加载");
            return;
        }
        
        log.info("🔄 开始批量预加载本地模板到咖啡缓存...");
        int totalLoaded = 0;

        try {
            for (String location : TEMPLATE_LOCATIONS) {
                log.debug("📂 搜索路径: {}", location);
                Resource[] resources = resourceResolver.getResources(location);
                log.info("📋 路径 {} 找到 {} 个模板文件", location, resources.length);

                for (Resource resource : resources) {
                    try {
                        loadSingleResourceToCache(resource);
                        totalLoaded++;
                    } catch (Exception e) {
                        log.warn("❌ 预加载模板失败: {} - {}", resource.getFilename(), e.getMessage());
                    }
                }
            }

            log.info("✅ 批量预加载完成，共预加载 {} 个本地模板到咖啡缓存", totalLoaded);

        } catch (Exception e) {
            log.error("❌ 批量预加载本地模板失败", e);
        }
    }

    /**
     * 🔑 加载单个资源文件到咖啡缓存
     */
    private void loadSingleResourceToCache(Resource resource) throws Exception {
        try (InputStream inputStream = resource.getInputStream()) {
            String content = IoUtil.read(inputStream, StandardCharsets.UTF_8);
            String filename = resource.getFilename();

            if (filename != null) {
                // 提取模板代码（不需要去掉.ureport.xml后缀）兼容　不带后缀
                String templateCode = filename;
                caffeineCache.put(templateCode, content);
                log.debug("✅ 预加载模板到咖啡缓存: {} -> {}", templateCode, resource.getURI());
            }
        }
    }

    /**
     * 清空缓存
     */
    public void clearCache() {
        caffeineCache.invalidateAll();
        log.info("清空咖啡缓存");
        // 重新加载
        loadAllTemplates();
    }
    
    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        boolean isDevMode = isDevelopmentMode();
        if (isDevMode) {
            return "开发模式：不使用缓存";
        } else {
            return String.format("咖啡缓存统计: %s", caffeineCache.stats());
        }
    }
    

    

}
