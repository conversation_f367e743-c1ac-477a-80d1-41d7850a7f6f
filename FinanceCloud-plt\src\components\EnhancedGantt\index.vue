<template>
  <div class="enhanced-gantt-container">
    <!-- 控制面板 -->
    <div class="control-panel">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            size="small"
            @change="loadGanttData">
          </el-date-picker>
        </el-col>
        <el-col :span="4">
          <el-select v-model="selectedWorkCenter" placeholder="工作中心" size="small" @change="loadGanttData">
            <el-option label="全部" value=""></el-option>
            <el-option 
              v-for="center in workCenters" 
              :key="center.id" 
              :label="center.centerName" 
              :value="center.id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="stockFilter" placeholder="库存状态" size="small" @change="loadGanttData">
            <el-option label="全部" value=""></el-option>
            <el-option label="库存充足" value="sufficient"></el-option>
            <el-option label="库存不足" value="insufficient"></el-option>
            <el-option label="无库存" value="none"></el-option>
            <el-option label="低于安全库存" value="below_safety"></el-option>
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" size="small" @click="loadGanttData">刷新</el-button>
          <el-button type="success" size="small" @click="exportGantt">导出</el-button>
          <el-button type="warning" size="small" @click="showStockAlert">库存预警</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 统计面板 -->
    <div class="stats-panel">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <span class="stats-label">总工单数</span>
              <span class="stats-value">{{ statistics.totalWorkOrders || 0 }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <span class="stats-label">平均完成率</span>
              <span class="stats-value" :class="getCompletionRateClass(statistics.avgCompletionRate)">
                {{ (statistics.avgCompletionRate || 0).toFixed(1) }}%
              </span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <span class="stats-label">库存预警</span>
              <span class="stats-value" :class="statistics.stockWarningCount > 0 ? 'warning' : 'normal'">
                {{ statistics.stockWarningCount || 0 }}
              </span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <span class="stats-label">延期工单</span>
              <span class="stats-value" :class="statistics.delayedCount > 0 ? 'danger' : 'normal'">
                {{ statistics.delayedCount || 0 }}
              </span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 甘特图容器 -->
    <div class="gantt-wrapper">
      <div id="gantt_here" class="gantt-container"></div>
    </div>

    <!-- 任务详情弹窗 -->
    <el-dialog title="工单详情" :visible.sync="taskDetailVisible" width="900px">
      <div v-if="selectedTask">
        <el-tabs v-model="activeTab">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="工单号">{{ selectedTask.workOrderCode }}</el-descriptions-item>
              <el-descriptions-item label="物料名称">{{ selectedTask.materialName }}</el-descriptions-item>
              <el-descriptions-item label="物料编码">{{ selectedTask.materialCode }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getStatusTagType(selectedTask.status)">{{ selectedTask.statusName }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="计划开始">{{ formatDateTime(selectedTask.planStartTime) }}</el-descriptions-item>
              <el-descriptions-item label="计划结束">{{ formatDateTime(selectedTask.planEndTime) }}</el-descriptions-item>
              <el-descriptions-item label="实际开始">{{ formatDateTime(selectedTask.actualStartTime) || '未开始' }}</el-descriptions-item>
              <el-descriptions-item label="实际结束">{{ formatDateTime(selectedTask.actualEndTime) || '未完成' }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <!-- 数量信息 -->
          <el-tab-pane label="数量信息" name="quantity">
            <div class="quantity-info">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-card">
                    <h4>生产数量</h4>
                    <div class="quantity-list">
                      <div class="quantity-item">
                        <span>计划数量：</span>
                        <span class="quantity-value">{{ selectedTask.planQty }}</span>
                      </div>
                      <div class="quantity-item">
                        <span>完成数量：</span>
                        <span class="quantity-value completed">{{ selectedTask.completedQty }}</span>
                      </div>
                      <div class="quantity-item">
                        <span>合格数量：</span>
                        <span class="quantity-value qualified">{{ selectedTask.qualifiedQty }}</span>
                      </div>
                      <div class="quantity-item">
                        <span>不良数量：</span>
                        <span class="quantity-value defective">{{ selectedTask.defectiveQty }}</span>
                      </div>
                      <div class="quantity-item">
                        <span>剩余数量：</span>
                        <span class="quantity-value remaining">{{ selectedTask.remainingQty }}</span>
                      </div>
                    </div>
                    <el-progress 
                      :percentage="parseFloat(selectedTask.completionRate || 0)" 
                      :color="getProgressColor(selectedTask.completionRate)"
                      :stroke-width="20"
                      :show-text="true">
                    </el-progress>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-card">
                    <h4>库存信息</h4>
                    <div class="stock-list">
                      <div class="stock-item">
                        <span>当前库存：</span>
                        <span class="stock-value">{{ selectedTask.totalCurrentStock }}</span>
                      </div>
                      <div class="stock-item">
                        <span>可用库存：</span>
                        <span class="stock-value">{{ selectedTask.totalAvailableStock }}</span>
                      </div>
                      <div class="stock-item">
                        <span>安全库存：</span>
                        <span class="stock-value">{{ selectedTask.totalSafetyStock }}</span>
                      </div>
                      <div class="stock-item">
                        <span>库存状态：</span>
                        <el-tag :type="getStockStatusType(selectedTask.stockStatus)">
                          {{ selectedTask.stockStatus }}
                        </el-tag>
                      </div>
                      <div class="stock-item">
                        <span>预警级别：</span>
                        <el-tag :type="getWarningLevelType(selectedTask.stockWarningLevel)">
                          {{ selectedTask.stockWarningLevel }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>

          <!-- 库存分布 -->
          <el-tab-pane label="库存分布" name="stock">
            <div class="stock-distribution">
              <h4>各仓库库存分布</h4>
              <el-table :data="parseStockDetails(selectedTask.stockDetails)" border size="small">
                <el-table-column prop="warehouse" label="仓库名称" width="200"></el-table-column>
                <el-table-column prop="stock" label="库存数量" width="120">
                  <template slot-scope="scope">
                    <span :class="scope.row.stock > 0 ? 'stock-positive' : 'stock-zero'">
                      {{ scope.row.stock }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.stock > 0 ? 'success' : 'danger'" size="small">
                      {{ scope.row.stock > 0 ? '有库存' : '无库存' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 库存预警弹窗 -->
    <el-dialog title="库存预警" :visible.sync="stockAlertVisible" width="800px">
      <el-table :data="stockWarnings" border size="small">
        <el-table-column prop="workOrderCode" label="工单号" width="150"></el-table-column>
        <el-table-column prop="materialName" label="物料名称" width="120"></el-table-column>
        <el-table-column prop="remainingQty" label="剩余需求" width="100"></el-table-column>
        <el-table-column prop="totalAvailableStock" label="可用库存" width="100"></el-table-column>
        <el-table-column prop="stockGap" label="库存缺口" width="100"></el-table-column>
        <el-table-column prop="urgencyLevel" label="紧急程度" width="100">
          <template slot-scope="scope">
            <el-tag :type="getUrgencyType(scope.row.urgencyLevel)" size="small">
              {{ scope.row.urgencyLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="suggestedAction" label="建议措施">
          <template slot-scope="scope">
            <span :class="getActionClass(scope.row.suggestedAction)">
              {{ scope.row.suggestedAction }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { gantt } from 'dhtmlx-gantt'
import { getEnhancedGanttData, getStockWarnings, getGanttStatistics } from '@/api/enhanced-gantt'

export default {
  name: 'EnhancedGantt',
  data() {
    return {
      dateRange: [],
      selectedWorkCenter: '',
      stockFilter: '',
      workCenters: [],
      ganttData: [],
      statistics: {},
      stockWarnings: [],
      taskDetailVisible: false,
      stockAlertVisible: false,
      selectedTask: null,
      activeTab: 'basic',
      loading: false
    }
  },
  mounted() {
    this.initDateRange()
    this.initGantt()
    this.loadGanttData()
  },
  beforeDestroy() {
    if (gantt) {
      gantt.clearAll()
    }
  },
  methods: {
    initDateRange() {
      const today = new Date()
      const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      this.dateRange = [
        lastMonth.toISOString().split('T')[0],
        today.toISOString().split('T')[0]
      ]
    },

    initGantt() {
      // 配置甘特图
      gantt.config.date_format = "%Y-%m-%d %H:%i:%s"
      gantt.config.scale_unit = "day"
      gantt.config.date_scale = "%m/%d"
      gantt.config.step = 1
      gantt.config.subscales = [
        { unit: "hour", step: 6, date: "%H:%i" }
      ]
      
      // 配置列
      gantt.config.columns = [
        { name: "text", label: "工单号", width: 150, tree: true },
        { name: "material_name", label: "物料", width: 120 },
        { name: "plan_qty", label: "计划", width: 80, align: "center" },
        { name: "completed_qty", label: "完成", width: 80, align: "center" },
        { name: "completion_rate", label: "完成率", width: 80, align: "center",
          template: function(obj) {
            return (obj.completion_rate || 0) + "%"
          }
        },
        { name: "stock_status", label: "库存", width: 100, align: "center",
          template: function(obj) {
            return `<span class="stock-status ${obj.stock_status_class}">${obj.stock_status || ''}</span>`
          }
        },
        { name: "start_date", label: "开始", width: 100, align: "center" },
        { name: "duration", label: "工期", width: 60, align: "center" }
      ]

      // 配置任务条样式
      gantt.templates.task_class = function(start, end, task) {
        let css = []
        
        // 根据状态设置样式
        if (task.status === 4) css.push('gantt-completed')
        else if (task.status === 3) css.push('gantt-paused')
        else if (task.status === 2) css.push('gantt-in-progress')
        else if (task.status === 7) css.push('gantt-cancelled')
        else css.push('gantt-not-started')
        
        // 根据库存状态设置边框
        if (task.stock_warning_level === '低于安全库存') css.push('stock-critical')
        else if (task.stock_warning_level === '库存不足') css.push('stock-warning')
        
        return css.join(' ')
      }

      // 配置任务文本
      gantt.templates.task_text = function(start, end, task) {
        return `${task.text} (${task.completion_rate || 0}%)`
      }

      // 配置进度条
      gantt.templates.progress_text = function(start, end, task) {
        return Math.round(task.progress * 100) + "% 完成"
      }

      // 点击事件
      gantt.attachEvent("onTaskClick", (id, e) => {
        const task = gantt.getTask(id)
        this.showTaskDetail(task)
        return false // 阻止默认行为
      })

      // 初始化甘特图
      gantt.init("gantt_here")
    },

    async loadGanttData() {
      if (this.loading) return
      this.loading = true

      try {
        const params = {
          startDate: this.dateRange[0],
          endDate: this.dateRange[1],
          workCenterId: this.selectedWorkCenter,
          stockFilter: this.stockFilter
        }

        // 并行加载数据
        const [ganttRes, statsRes] = await Promise.all([
          getEnhancedGanttData(params),
          getGanttStatistics(params)
        ])

        if (ganttRes.code === 200) {
          this.ganttData = ganttRes.data || []
          this.updateGanttChart()
        }

        if (statsRes.code === 200) {
          this.statistics = statsRes.data || {}
        }

      } catch (error) {
        console.error('加载甘特图数据失败:', error)
        this.$message.error('加载甘特图数据失败')
      } finally {
        this.loading = false
      }
    },

    updateGanttChart() {
      const tasks = this.transformToGanttTasks(this.ganttData)
      gantt.clearAll()
      gantt.parse({ data: tasks })
    },

    transformToGanttTasks(data) {
      return data.map((item, index) => ({
        id: item.workOrderId,
        text: item.workOrderCode,
        material_name: item.materialName,
        plan_qty: item.planQty,
        completed_qty: item.completedQty,
        completion_rate: parseFloat(item.completionRate || 0),
        stock_status: item.stockStatus,
        stock_status_class: this.getStockStatusClass(item.stockStatus),
        start_date: this.formatGanttDate(item.planStartTime),
        end_date: this.formatGanttDate(item.planEndTime),
        duration: item.planDuration || 1,
        progress: (parseFloat(item.completionRate || 0) / 100),
        status: item.status,
        stock_warning_level: item.stockWarningLevel,
        // 保存原始数据用于详情显示
        originalData: item
      }))
    },

    formatGanttDate(dateStr) {
      if (!dateStr) return new Date()
      return new Date(dateStr.replace(/-/g, '/'))
    },

    getStockStatusClass(status) {
      const classMap = {
        '库存充足': 'sufficient',
        '库存不足': 'insufficient',
        '无库存': 'none'
      }
      return classMap[status] || ''
    },

    showTaskDetail(task) {
      this.selectedTask = task.originalData
      this.taskDetailVisible = true
      this.activeTab = 'basic'
    },

    async showStockAlert() {
      try {
        const params = {
          startDate: this.dateRange[0],
          endDate: this.dateRange[1]
        }

        const response = await getStockWarnings(params)
        if (response.code === 200) {
          this.stockWarnings = response.data || []
          this.stockAlertVisible = true
        }
      } catch (error) {
        console.error('获取库存预警失败:', error)
        this.$message.error('获取库存预警失败')
      }
    },

    exportGantt() {
      // 导出功能
      gantt.exportToPDF({
        name: "生产计划执行甘特图.pdf"
      })
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      return new Date(dateTime).toLocaleString('zh-CN')
    },

    // 解析库存详情
    parseStockDetails(stockDetails) {
      if (!stockDetails) return []
      return stockDetails.split('; ').map(detail => {
        const [warehouse, stock] = detail.split(':')
        return {
          warehouse: warehouse || '',
          stock: parseFloat(stock) || 0
        }
      })
    },

    // 样式相关方法
    getCompletionRateClass(rate) {
      if (rate >= 100) return 'normal'
      if (rate >= 80) return 'normal'
      if (rate >= 60) return 'warning'
      return 'danger'
    },

    getStatusTagType(status) {
      const typeMap = {
        1: 'info',     // 未开始
        2: 'primary',  // 进行中
        3: 'warning',  // 已暂停
        4: 'success',  // 已完成
        7: 'danger'    // 已取消
      }
      return typeMap[status] || 'info'
    },

    getStockStatusType(status) {
      const typeMap = {
        '库存充足': 'success',
        '库存不足': 'warning',
        '无库存': 'danger'
      }
      return typeMap[status] || 'info'
    },

    getWarningLevelType(level) {
      const typeMap = {
        '正常': 'success',
        '库存不足': 'warning',
        '低于安全库存': 'danger'
      }
      return typeMap[level] || 'info'
    },

    getUrgencyType(urgency) {
      const typeMap = {
        '紧急': 'danger',
        '高': 'warning',
        '中': 'primary',
        '低': 'info'
      }
      return typeMap[urgency] || 'info'
    },

    getActionClass(action) {
      if (action.includes('立即') || action.includes('紧急')) return 'action-urgent'
      if (action.includes('计划')) return 'action-plan'
      return 'action-normal'
    },

    getProgressColor(progress) {
      const rate = parseFloat(progress || 0)
      if (rate >= 100) return '#4caf50'
      if (rate >= 80) return '#8bc34a'
      if (rate >= 60) return '#ffc107'
      if (rate >= 40) return '#ff9800'
      return '#f44336'
    }
  }
}
</script>

<style scoped>
.enhanced-gantt-container {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.control-panel {
  margin-bottom: 15px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
}

.stats-panel {
  margin-bottom: 15px;
}

.stats-card {
  text-align: center;
  height: 80px;
}

.stats-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  gap: 5px;
}

.stats-label {
  font-size: 12px;
  color: #666;
}

.stats-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.stats-value.warning { color: #ff9800; }
.stats-value.danger { color: #f44336; }
.stats-value.normal { color: #4caf50; }

.gantt-wrapper {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.gantt-container {
  width: 100%;
  height: 100%;
}

.quantity-info, .stock-distribution {
  margin-top: 20px;
}

.info-card {
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  background: #fafafa;
}

.quantity-list, .stock-list {
  margin: 15px 0;
}

.quantity-item, .stock-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.quantity-value.completed { color: #4caf50; }
.quantity-value.qualified { color: #2196f3; }
.quantity-value.defective { color: #f44336; }
.quantity-value.remaining { color: #ff9800; }

.stock-positive { color: #4caf50; font-weight: bold; }
.stock-zero { color: #f44336; font-weight: bold; }
</style>

<style>
/* 甘特图样式 */
.gantt-completed { background-color: #4caf50 !important; }
.gantt-paused { background-color: #ff9800 !important; }
.gantt-in-progress { background-color: #2196f3 !important; }
.gantt-cancelled { background-color: #9e9e9e !important; }
.gantt-not-started { background-color: #e0e0e0 !important; }

.stock-critical { border: 3px solid #f44336 !important; }
.stock-warning { border: 2px solid #ff9800 !important; }

.stock-status.sufficient { color: #4caf50; }
.stock-status.insufficient { color: #ff9800; }
.stock-status.none { color: #f44336; }
</style>
