　import request from '@/router/axios'

/**
 * 获取增强版甘特图数据（包含库存信息）
 * @param {Object} params 查询参数
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @param {string} params.workCenterId 工作中心ID（可选）
 * @param {string} params.stockFilter 库存筛选条件（可选）
 * @returns {Promise}
 */
export function getEnhancedGanttData(params) {
  return request({
    url: '/api/production/gantt/enhanced-data',
    method: 'get',
    params: params
  })
}

/**
 * 获取库存预警信息
 * @param {Object} params 查询参数
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @returns {Promise}
 */
export function getStockWarnings(params) {
  return request({
    url: '/api/production/gantt/stock-warnings',
    method: 'get',
    params: params
  })
}

/**
 * 获取甘特图统计信息
 * @param {Object} params 查询参数
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @param {string} params.workCenterId 工作中心ID（可选）
 * @returns {Promise}
 */
export function getGanttStatistics(params) {
  return request({
    url: '/api/production/gantt/statistics',
    method: 'get',
    params: params
  })
}

/**
 * 获取工作中心列表
 * @returns {Promise}
 */
export function getWorkCenters() {
  return request({
    url: '/api/production/work-centers',
    method: 'get'
  })
}

/**
 * 导出甘特图数据
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function exportGanttData(params) {
  return request({
    url: '/api/production/gantt/export',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}
