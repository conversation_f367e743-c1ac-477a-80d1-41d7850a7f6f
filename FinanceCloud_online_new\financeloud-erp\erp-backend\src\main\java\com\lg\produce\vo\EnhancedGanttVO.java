package com.lg.produce.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 增强版甘特图数据VO（包含库存信息）
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@ApiModel(description = "增强版甘特图数据")
public class EnhancedGanttVO {

    @ApiModelProperty("工单ID")
    private String workOrderId;

    @ApiModelProperty("工单编号")
    private String workOrderCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("计划开始时间")
    private LocalDateTime planStartTime;

    @ApiModelProperty("计划结束时间")
    private LocalDateTime planEndTime;

    @ApiModelProperty("实际开始时间")
    private LocalDateTime actualStartTime;

    @ApiModelProperty("实际结束时间")
    private LocalDateTime actualEndTime;

    @ApiModelProperty("执行状态")
    private Integer status;

    @ApiModelProperty("状态名称")
    private String statusName;

    // 数量信息
    @ApiModelProperty("计划数量")
    private BigDecimal planQty;

    @ApiModelProperty("完成数量")
    private BigDecimal completedQty;

    @ApiModelProperty("合格数量")
    private BigDecimal qualifiedQty;

    @ApiModelProperty("不良数量")
    private BigDecimal defectiveQty;

    @ApiModelProperty("剩余数量")
    private BigDecimal remainingQty;

    @ApiModelProperty("完成率")
    private BigDecimal completionRate;

    @ApiModelProperty("合格率")
    private BigDecimal qualifiedRate;

    // 库存信息
    @ApiModelProperty("总当前库存")
    private BigDecimal totalCurrentStock;

    @ApiModelProperty("总可用库存")
    private BigDecimal totalAvailableStock;

    @ApiModelProperty("总安全库存")
    private BigDecimal totalSafetyStock;

    @ApiModelProperty("是否低于安全库存")
    private Boolean isBelowSafetyStock;

    @ApiModelProperty("库存详情")
    private String stockDetails;

    @ApiModelProperty("库存状态")
    private String stockStatus;

    @ApiModelProperty("库存预警级别")
    private String stockWarningLevel;

    // 时间信息
    @ApiModelProperty("计划工期（天）")
    private Integer planDuration;

    @ApiModelProperty("实际工期（天）")
    private Integer actualDuration;

    @ApiModelProperty("延期天数")
    private Integer delayDays;

    // 工作中心信息
    @ApiModelProperty("主要工作中心ID")
    private String primaryWorkCenterId;

    @ApiModelProperty("主要工作中心名称")
    private String primaryWorkCenterName;

    // 任务统计
    @ApiModelProperty("任务总数")
    private Integer totalTasks;

    @ApiModelProperty("已完成任务数")
    private Integer completedTasks;

    @ApiModelProperty("异常次数")
    private Integer exceptionCount;

    @ApiModelProperty("异常类型")
    private String exceptionTypes;

    @ApiModelProperty("备注")
    private String remarks;
}
